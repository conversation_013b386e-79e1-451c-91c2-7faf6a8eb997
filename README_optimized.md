# 高性能签名验证优化版本

## 概述

本项目提供了高性能的签名验证实现，在保持与 Python 版本完全一致的前提下，通过指针操作、零拷贝优化和内存管理优化来显著提升性能。

## 文件结构

- `signature.go` - 原始版本（保持不变）
- `sign.py` - Python 基准版本（保持不变）
- `signature_optimized.go` - 高性能优化版本
- `signature_test.go` - 测试和性能对比
- `README_optimized.md` - 本说明文档

## 主要优化特性

### 1. 零拷贝字符串转换
```go
// 使用 unsafe 包避免内存分配
func stringToBytesOptimized(s string) []byte {
    return unsafe.Slice(unsafe.StringData(s), len(s))
}
```

### 2. 指针优化访问
```go
// 预定义模数列表指针，避免重复访问全局变量
var (
    modPrimeList1Ptr = &ModPrimeList1
    modPrimeList2Ptr = &ModPrimeList2
)
```

### 3. 循环展开优化
```go
// 处理4个字节为一组，减少循环开销
for i+3 < inputLen {
    // 批量处理4个字节
    i += 4
}
```

### 4. 内联计算
- 内联48位签名转换，避免函数调用开销
- 内联字节转整数计算，使用 switch 语句优化

## 核心函数

### Sign48PythonOptimized
高性能版本的48位签名计算
```go
func Sign48PythonOptimized(input string) uint64
```

### Get64SignAddPrefixPythonOptimized
高性能版本的带前缀64位签名计算
```go
func Get64SignAddPrefixPythonOptimized(key string, prefix uint64) uint64
```

### 批量处理函数
```go
func BatchSign48PythonOptimized(inputs []string) []uint64
func BatchGet64SignAddPrefixPythonOptimized(keys []string, prefix uint64) []uint64
```

## 使用方法

### 基本使用
```go
package main

import (
    "fmt"
    "your_project/predict"
)

func main() {
    input := "hello world"
    
    // 48位签名
    sign48 := predict.Sign48PythonOptimized(input)
    fmt.Printf("48位签名: %d\n", sign48)
    
    // 带前缀64位签名
    prefix := uint64(255)
    sign64 := predict.Get64SignAddPrefixPythonOptimized(input, prefix)
    fmt.Printf("带前缀64位签名: %d\n", sign64)
}
```

### 批量处理
```go
inputs := []string{"test1", "test2", "test3"}
results := predict.BatchSign48PythonOptimized(inputs)
```

## 运行测试

### 一致性测试
```bash
go test -v -run TestSign48Consistency
go test -v -run TestGet64SignAddPrefixConsistency
```

### 性能基准测试
```bash
go test -bench=BenchmarkSign48Python -benchmem
go test -bench=BenchmarkGet64SignAddPrefixPython -benchmem
```

### 性能对比测试
```bash
go test -v -run TestPerformanceComparison
```

## 性能优势

### 主要优化点
1. **零拷贝转换**: 避免字符串到字节数组的内存分配
2. **指针访问**: 减少全局变量访问开销
3. **循环展开**: 减少循环迭代次数
4. **内联计算**: 避免函数调用开销
5. **预分配**: 批量处理时预分配结果切片

### 预期性能提升
- 单次签名计算: 2-5x 性能提升
- 批量处理: 3-8x 性能提升
- 内存分配: 显著减少

## 兼容性保证

### 算法一致性
- 与 Python 版本保持完全相同的算法逻辑
- 相同的位运算顺序和模数运算方式
- 通过全面的测试用例验证一致性

### 向后兼容
- 保留原有函数不变
- 新增优化版本函数
- 清晰的函数命名区分

## 注意事项

### 安全性
- 使用 `unsafe` 包进行零拷贝转换
- 仅在字符串不可变的前提下安全
- 不修改原始字符串数据

### 适用场景
- 高频签名计算场景
- 批量数据处理
- 性能敏感的应用

### 不适用场景
- 对内存安全要求极高的场景
- 需要跨平台兼容性的场景（unsafe 包可能有平台差异）

## 开发建议

1. 在生产环境使用前，请充分测试一致性
2. 监控内存使用情况，确保无内存泄漏
3. 根据实际使用场景选择合适的函数版本
4. 定期运行性能基准测试，监控性能变化

## 贡献

如果发现性能问题或一致性问题，请及时反馈。我们会持续优化性能，同时确保与 Python 版本的完全一致性。
