package predict

import (
	"unsafe"
)
// 预定义的模数列表指针（避免重复访问全局变量）
var (
	modPrimeList1Ptr = &ModPrimeList1
	modPrimeList2Ptr = &ModPrimeList2
)


func stringToBytesOptimized(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

func get48SignFrom64Optimized(s uint64) uint64 {
	const MASK48 = 0xFFFFFFFFFFFF
	s3 := (s >> 48) & 0xFFFF // 提取高16位
	s = s & MASK48           // 保留低48位
	s ^= s3                  // 与高16位异或
	s ^= (s3 << 8)           // 多次位移异或
	s ^= (s3 << 16)
	s ^= (s3 << 24)
	s ^= (s3 << 32)
	return s
}

func getSignFromBytesOptimized(input []byte, modListPtr *[]uint64) uint64 {
	var res uint64 = 0
	modList := *modListPtr // 解引用一次，避免重复解引用
	inputLen := len(input)

	i := 0
	for i+3 < inputLen {
		id0 := i & 0xFF
		id1 := (i + 1) & 0xFF
		id2 := (i + 2) & 0xFF
		id3 := (i + 3) & 0xFF
		
		res += uint64(input[i]) * modList[id0]
		res += uint64(input[i+1]) * modList[id1]
		res += uint64(input[i+2]) * modList[id2]
		res += uint64(input[i+3]) * modList[id3]
		res &= 0xFFFFFFFF // 保持32位
		
		i += 4
	}
	
	// 处理剩余字节
	for i < inputLen {
		id := i & 0xFF
		res += uint64(input[i]) * modList[id]
		res &= 0xFFFFFFFF // 保持32位
		i++
	}
	
	return res
}

func strToIntFromBytesOptimized(input []byte) uint64 {
	inputLen := len(input)
	if inputLen == 0 || inputLen > 4 {
		return 0
	}

	var res uint64 = 0

	switch inputLen {
	case 1:
		res = uint64(input[0])
	case 2:
		res = uint64(input[1])<<8 + uint64(input[0])
	case 3:
		res = uint64(input[2])<<16 + uint64(input[1])<<8 + uint64(input[0])
	case 4:
		res = uint64(input[3])<<24 + uint64(input[2])<<16 + uint64(input[1])<<8 + uint64(input[0])
	}
	
	return res
}

// 高性能版本：Python兼容的字节数组签名函数
// 使用零拷贝和指针优化，保持与 Python 版本完全一致的逻辑
func SignBytesOptimized(input []byte) uint64 {
	inputLen := len(input)
	if inputLen == 0 {
		return 0
	}

	if inputLen <= 4 {
		// 短字符串处理：转换为整数并左移32位
		res := strToIntFromBytesOptimized(input)
		return res << 32
	} else if inputLen <= 8 {
		// 中等长度字符串：分两部分处理
		sign1 := strToIntFromBytesOptimized(input[0:4])
		var sign2 uint64
		if inputLen > 4 {
			sign2 = strToIntFromBytesOptimized(input[4:inputLen])
		}
		return (sign1 << 32) + sign2
	} else {
		// 长字符串：使用两个模数列表
		sign1 := getSignFromBytesOptimized(input, modPrimeList1Ptr)
		sign2 := getSignFromBytesOptimized(input, modPrimeList2Ptr)
		return (sign1 << 32) + sign2
	}
}

func Sign48PythonOptimized(input string) uint64 {
	// 零拷贝字符串转字节
	inputBytes := stringToBytesOptimized(input)
	value := SignBytesOptimized(inputBytes)
	
	// 分离高低32位并交换
	high := value >> 32
	low := value & 0xFFFFFFFF
	signOut := (low << 32) + high
	
	// 内联48位转换
	return get48SignFrom64Optimized(signOut)
}

// 高性能版本：获取带前缀的64位签名（零拷贝优化）
// 使用零拷贝和内联优化，保持与 Python 版本的逻辑一致
func Get64SignAddPrefixPythonOptimized(key string, prefix uint64) uint64 {
	// 零拷贝字符串转字节
	keyBytes := stringToBytesOptimized(key)
	value := SignBytesOptimized(keyBytes)
	
	// 分离高低32位并交换
	high := value >> 32
	low := value & 0xFFFFFFFF
	signOut := (low << 32) + high
	
	// 内联48位转换
	signOut = get48SignFrom64Optimized(signOut)
	
	// 按位或操作（与 Python 版本一致）
	return signOut | (prefix << 48)
}

// 高性能版本：批量签名计算（用于批量处理场景）
// 预分配结果切片，减少内存分配
func BatchSign48PythonOptimized(inputs []string) []uint64 {
	results := make([]uint64, len(inputs))
	
	for i, input := range inputs {
		results[i] = Sign48PythonOptimized(input)
	}
	
	return results
}


func BatchGet64SignAddPrefixPythonOptimized(keys []string, prefix uint64) []uint64 {
	results := make([]uint64, len(keys))
	
	for i, key := range keys {
		results[i] = Get64SignAddPrefixPythonOptimized(key, prefix)
	}
	
	return results
}
