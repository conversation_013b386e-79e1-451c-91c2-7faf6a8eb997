package predict

import (
	"fmt"
	"testing"
	"time"
)

// 测试数据
var testCases = []string{
	"hello",
	"world",
	"test",
	"1234",
	"abcdefgh",
	"这是中文测试",
	"ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】",
	"",
	"a",
	"ab",
	"abc",
	"abcd",
	"abcde",
	"abcdef",
	"abcdefg",
	"abcdefgh",
	"abcdefghi",
	"1",
	"12",
	"123",
	"1234567890",
}

// 测试48位签名一致性
func TestSign48Consistency(t *testing.T) {
	fmt.Println("=== 测试48位签名一致性 ===")
	
	for i, testCase := range testCases {
		original := Sign48Python(testCase)
		optimized := Sign48PythonOptimized(testCase)
		
		if original != optimized {
			t.Errorf("测试用例 %d 失败: 输入='%s', 原版本=%d, 优化版本=%d", 
				i, testCase, original, optimized)
		} else {
			fmt.Printf("✓ 测试用例 %d: 输入='%s', 结果=%d\n", i, testCase, original)
		}
	}
}

// 测试带前缀64位签名一致性
func TestGet64SignAddPrefixConsistency(t *testing.T) {
	fmt.Println("\n=== 测试带前缀64位签名一致性 ===")
	
	prefixes := []uint64{0, 1, 255, 65535}
	
	for _, prefix := range prefixes {
		fmt.Printf("\n--- 前缀: %d ---\n", prefix)
		for i, testCase := range testCases {
			original := Get64SignAddPrefixPython(testCase, prefix)
			optimized := Get64SignAddPrefixPythonOptimized(testCase, prefix)
			
			if original != optimized {
				t.Errorf("测试用例 %d 失败: 输入='%s', 前缀=%d, 原版本=%d, 优化版本=%d", 
					i, testCase, prefix, original, optimized)
			} else {
				fmt.Printf("✓ 测试用例 %d: 输入='%s', 结果=%d\n", i, testCase, original)
			}
		}
	}
}

// 性能基准测试 - 48位签名
func BenchmarkSign48Python(b *testing.B) {
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Sign48Python(testInput)
	}
}

func BenchmarkSign48PythonOptimized(b *testing.B) {
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Sign48PythonOptimized(testInput)
	}
}

// 性能基准测试 - 带前缀64位签名
func BenchmarkGet64SignAddPrefixPython(b *testing.B) {
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	prefix := uint64(255)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Get64SignAddPrefixPython(testInput, prefix)
	}
}

func BenchmarkGet64SignAddPrefixPythonOptimized(b *testing.B) {
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	prefix := uint64(255)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Get64SignAddPrefixPythonOptimized(testInput, prefix)
	}
}

// 性能基准测试 - 批量处理
func BenchmarkBatchSign48PythonOptimized(b *testing.B) {
	inputs := make([]string, 100)
	for i := range inputs {
		inputs[i] = fmt.Sprintf("test_input_%d", i)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		BatchSign48PythonOptimized(inputs)
	}
}

// 性能对比测试
func TestPerformanceComparison(t *testing.T) {
	fmt.Println("\n=== 性能对比测试 ===")
	
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	iterations := 100000
	
	// 测试原版本
	start := time.Now()
	for i := 0; i < iterations; i++ {
		Sign48Python(testInput)
	}
	originalTime := time.Since(start)
	
	// 测试优化版本
	start = time.Now()
	for i := 0; i < iterations; i++ {
		Sign48PythonOptimized(testInput)
	}
	optimizedTime := time.Since(start)
	
	speedup := float64(originalTime) / float64(optimizedTime)
	
	fmt.Printf("原版本耗时: %v\n", originalTime)
	fmt.Printf("优化版本耗时: %v\n", optimizedTime)
	fmt.Printf("性能提升: %.2fx\n", speedup)
	
	if speedup < 1.0 {
		t.Logf("警告: 优化版本性能未提升，可能需要进一步优化")
	}
}

// 示例用法
func ExampleUsage() {
	fmt.Println("\n=== 使用示例 ===")
	
	testInput := "hello world"
	
	// 48位签名
	sign48 := Sign48PythonOptimized(testInput)
	fmt.Printf("48位签名: %d\n", sign48)
	
	// 带前缀64位签名
	prefix := uint64(255)
	sign64 := Get64SignAddPrefixPythonOptimized(testInput, prefix)
	fmt.Printf("带前缀64位签名: %d\n", sign64)
	
	// 批量处理
	inputs := []string{"test1", "test2", "test3"}
	batchResults := BatchSign48PythonOptimized(inputs)
	fmt.Printf("批量48位签名: %v\n", batchResults)
}
