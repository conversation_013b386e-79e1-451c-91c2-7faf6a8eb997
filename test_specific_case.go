package main

import (
	"fmt"
)

func main() {
	fmt.Println("=== Go 版本测试结果 ===")

	// 用户指定的测试字符串
	testInput := "惆怅长岑长擦擦擦擦擦擦擦擦擦擦擦擦aaaaaaaaaaaaaaa111111111111111111114444444444445555555555555555550000000000000007777777*******&&&&&&&&&######@@@"
	slotID := uint64(66)

	fmt.Printf("测试字符串: %s\n", testInput)
	fmt.Printf("Slot ID: %d\n", slotID)
	fmt.Println()

	// 48位签名 - 原版本
	sign48Original := Sign48Python(testInput)
	fmt.Printf("Go 原版本 sign48 结果: %d\n", sign48Original)

	// 48位签名 - 优化版本
	sign48Optimized := Sign48PythonOptimized(testInput)
	fmt.Printf("Go 优化版本 sign48 结果: %d\n", sign48Optimized)

	// 检查一致性
	fmt.Printf("Go 版本内部一致性: %t\n", sign48Original == sign48Optimized)

	fmt.Println()

	// 带前缀64位签名 - 原版本
	sign64Original := Get64SignAddPrefixPython(testInput, slotID)
	fmt.Printf("Go 原版本 get_64sign_add_prefix (prefix=%d) 结果: %d\n", slotID, sign64Original)

	// 带前缀64位签名 - 优化版本
	sign64Optimized := Get64SignAddPrefixPythonOptimized(testInput, slotID)
	fmt.Printf("Go 优化版本 get_64sign_add_prefix (prefix=%d) 结果: %d\n", slotID, sign64Optimized)

	// 检查一致性
	fmt.Printf("Go 版本内部一致性: %t\n", sign64Original == sign64Optimized)

	fmt.Println()
	fmt.Println("=== 与 Python 版本对比 ===")
	fmt.Printf("Python sign48: 104402393976623\n")
	fmt.Printf("Go sign48: %d\n", sign48Optimized)
	fmt.Printf("48位签名一致性: %t\n", sign48Optimized == 104402393976623)

	fmt.Printf("Python get_64sign_add_prefix: 18681750856879919\n")
	fmt.Printf("Go get_64sign_add_prefix: %d\n", sign64Optimized)
	fmt.Printf("64位签名一致性: %t\n", sign64Optimized == 18681750856879919)

	fmt.Println()
	fmt.Println("--- 原测试字符串对比 ---")
	oldTestInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"

	oldSign48 := Sign48PythonOptimized(oldTestInput)
	oldSign64 := Get64SignAddPrefixPythonOptimized(oldTestInput, 0)

	fmt.Printf("Go sign48 (原字符串): %d\n", oldSign48)
	fmt.Printf("Python sign48 (原字符串): 15026192636179\n")
	fmt.Printf("原字符串48位一致性: %t\n", oldSign48 == 15026192636179)

	fmt.Printf("Go get_64sign_add_prefix (原字符串, prefix=0): %d\n", oldSign64)
	fmt.Printf("Python get_64sign_add_prefix (原字符串, prefix=0): 15026192636179\n")
	fmt.Printf("原字符串64位一致性: %t\n", oldSign64 == 15026192636179)
}
