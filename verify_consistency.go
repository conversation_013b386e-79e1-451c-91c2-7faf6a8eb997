package main

import (
	"fmt"
	"hpconsistentsignverification/predict"
)

func main() {
	// 使用与 Python 版本相同的测试字符串
	testInput := "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
	
	fmt.Println("=== 与 Python 版本一致性验证 ===")
	
	// 48位签名
	sign48Original := predict.Sign48Python(testInput)
	sign48Optimized := predict.Sign48PythonOptimized(testInput)
	
	fmt.Printf("Python 版本 sign48 结果: 15026192636179\n")
	fmt.Printf("Go 原版本 sign48 结果: %d\n", sign48Original)
	fmt.Printf("Go 优化版本 sign48 结果: %d\n", sign48Optimized)
	fmt.Printf("一致性检查: %t\n", sign48Original == sign48Optimized && sign48Original == 15026192636179)
	
	fmt.Println()
	
	// 带前缀64位签名
	prefix := uint64(0)
	sign64Original := predict.Get64SignAddPrefixPython(testInput, prefix)
	sign64Optimized := predict.Get64SignAddPrefixPythonOptimized(testInput, prefix)
	
	fmt.Printf("Python 版本 get_64sign_add_prefix 结果: 15026192636179\n")
	fmt.Printf("Go 原版本 get_64sign_add_prefix 结果: %d\n", sign64Original)
	fmt.Printf("Go 优化版本 get_64sign_add_prefix 结果: %d\n", sign64Optimized)
	fmt.Printf("一致性检查: %t\n", sign64Original == sign64Optimized && sign64Original == 15026192636179)
	
	fmt.Println()
	
	// 测试其他输入
	testCases := []string{
		"hello",
		"world", 
		"test",
		"1234",
		"这是中文测试",
		"",
		"a",
		"ab",
		"abc",
		"abcd",
	}
	
	fmt.Println("=== 多种输入测试 ===")
	for i, input := range testCases {
		original := predict.Sign48Python(input)
		optimized := predict.Sign48PythonOptimized(input)
		consistent := original == optimized
		
		fmt.Printf("测试 %d: 输入='%s', 原版本=%d, 优化版本=%d, 一致=%t\n", 
			i+1, input, original, optimized, consistent)
	}
}
