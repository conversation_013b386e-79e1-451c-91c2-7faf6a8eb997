#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sign import sign48, get_64sign_add_prefix

def main():
    print("=== Python 版本测试结果 ===")
    
    # 使用相同的测试字符串
    test_input = "ad去我二姐i取238让1323《》：」｜』『【pafj去asdjhfoabciasocnd[akdf'afkdasdfnashjdboqwfhej[nrn1y83gr0123rh123rnasdkasdla、阿十六大屡屡、af哦3jradslfqawepfqwenf·～qrq-=——//，。，】"
    
    # 48位签名
    sign48_result = sign48(test_input)
    print(f"Python sign48 结果: {sign48_result}")
    
    # 带前缀64位签名 (prefix=0)
    sign64_result = get_64sign_add_prefix(test_input, 0)
    print(f"Python get_64sign_add_prefix (prefix=0) 结果: {sign64_result}")
    
    print("\n=== 多种输入测试 ===")
    test_cases = [
        "hello",
        "world", 
        "test",
        "1234",
        "这是中文测试",
        "",
        "a",
        "ab",
        "abc",
        "abcd",
    ]
    
    for i, input_str in enumerate(test_cases):
        result = sign48(input_str)
        print(f"测试 {i+1}: 输入='{input_str}', Python结果={result}")

if __name__ == "__main__":
    main()
